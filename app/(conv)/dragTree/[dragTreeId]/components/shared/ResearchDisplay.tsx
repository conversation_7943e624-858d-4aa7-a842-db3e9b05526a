import React, {
  useState,
  useEffect,
  useRef,
  useCallback,
  ErrorInfo,
  ReactNode,
  useMemo,
} from 'react'
// Textarea removed in v2 flow for streaming; keep import commented if needed later
// import { Textarea } from '@/components/ui/textarea'
import { AlertTriangle, Loader2 } from 'lucide-react'
import { DragTreeNodeContentStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useResearchLifecycle } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useResearchLifecycle'
import { cn } from '@/lib/utils'
import debounce from 'lodash/debounce'
import { useUIStore } from '@/app/stores/ui_store'

import {
  SearchProgressIndicator,
  hasSearchResults,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'
import TiptapQuickResearchEditor from '@/app/components/editor/TiptapQuickResearchEditor'
import { JSONContent } from '@tiptap/react'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { getTierPermissions } from '@/app/configs/tier-permissions'
import { shallow } from 'zustand/shallow'
import toast from 'react-hot-toast'
import { logEventWithContext } from '@/app/libs/logging'
import { extractNodeContentText } from '@/app/utils/nodeContent'
import type { NodeContentItem } from '@/app/stores/dragtree_store/store'
import QuickResearchStreamingDisplay from './QuickResearchStreamingDisplay'
import { QuickResearchHeaderSection } from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchHeaderSection'
import { QuickResearchStatusFooter } from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchStatusFooter'

// Simple error boundary for research operations
class ResearchErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('[Research] Error boundary caught error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              <span>Research component error</span>
            </div>
          </div>
        )
      )
    }

    return this.props.children
  }
}

type ResearchDisplayProps = {
  nodeId: string
  questionText: string
  contentId?: string
  className?: string
  enableHoverPreview?: boolean // Control whether to show on general hover
  /**
   * When true, the content will be constrained to a specific height and enable scrolling.
   * Used in tab view or other contexts where height should be limited.
   */
  constrainHeight?: boolean
}

type CopyOrigin = 'stream' | 'stored' | 'local'

const ResearchDisplay: React.FC<ResearchDisplayProps> = ({
  nodeId,
  questionText,
  contentId,
  className = '',
  enableHoverPreview = false,
  constrainHeight = false,
}) => {
  // Permissions: determine read-only for quick research editing
  const { data: session } = useSession()
  // Use only session/DB tier (no local overrides)
  const tier = ((session?.user as any)?.subscription?.tier ||
    (session?.user as any)?.subscription_tier ||
    SubscriptionTier.FREE) as SubscriptionTier
  const { canEditQuickResearch } = getTierPermissions(tier)
  const isReadOnlyDueToPermissions = !canEditQuickResearch
  // --- STATE MANAGEMENT ---
  const nodeContentMap = useDragTreeStore(
    state => state.nodeContent.get(nodeId),
    shallow
  )
  const updateNodeContent = useDragTreeStore(state => state.updateNodeContent)
  const fetchNodeContent = useDragTreeStore(state => state.fetchNodeContent)
  const getNodePath = useDragTreeStore(state => state.getNodePath)
  const isContentRead = useDragTreeStore(state => state.isContentRead)
  const markContentAsReadOptimistic = useDragTreeStore(
    state => state.markContentAsReadOptimistic
  )
  const dragTreeId = useDragTreeStore(state => state.dragTreeId)

  const { setHoverCollapseLock } = useUIStore()
  // Removed researchPreviewNodeId to fix shared state and rerender issues
  const { addTab } = useTabStore()

  // --- DERIVED STATE & LIFECYCLE ---
  const fullQuestionPath = React.useMemo(
    () => getNodePath(nodeId) || questionText,
    [nodeId, questionText, getNodePath]
  )

  const {
    isStreaming,
    streamingContent,
    startResearch,
    activeContentId,
    messages,
  } = useResearchLifecycle({
    nodeId,
    questionText: fullQuestionPath,
  })

  // State for controlling when to show TipTap editor vs streaming display
  const [showTipTapEditor, setShowTipTapEditor] = useState<boolean>(false)

  // Callback to convert streaming content to TipTap editor
  const handleConvertToEditor = useCallback((content: string) => {
    setLocalContent(content)
    setShowTipTapEditor(true)
  }, [])

  // Memoize all heavy derived computations
  const { workingContentId, status, storeContent, stepMessages, storedNodeContent } =
    useMemo(() => {
      const workingContentIdLocal =
        contentId ||
        activeContentId ||
        (nodeContentMap && nodeContentMap.size > 0
          ? Array.from(nodeContentMap.keys())[0]
          : null)

      const storedNodeContentLocal: NodeContentItem | null = workingContentIdLocal
        ? nodeContentMap?.get(workingContentIdLocal) ?? null
        : null

      const statusLocal = storedNodeContentLocal?.status || null
      const storeContentLocal = storedNodeContentLocal?.contentText || ''

      const searchResultsLocal =
        storedNodeContentLocal?.metadata &&
        hasSearchResults(storedNodeContentLocal.metadata)
          ? storedNodeContentLocal.metadata.searchResults
          : null

      // Prefer messages column (UIMessage[]) for Steps; fallback to metadata.messages for backward compatibility
      const stepMessagesLocal = Array.isArray(storedNodeContentLocal?.messages)
        ? (storedNodeContentLocal!.messages as any[]) || []
        : storedNodeContentLocal?.metadata &&
            Array.isArray((storedNodeContentLocal.metadata as any).messages)
          ? ((storedNodeContentLocal.metadata as any).messages as any[])
          : []

      return {
        workingContentId: workingContentIdLocal,
        storedNodeContent: storedNodeContentLocal,
        status: statusLocal,
        storeContent: storeContentLocal,
        searchResults: searchResultsLocal,
        stepMessages: stepMessagesLocal,
      }
    }, [contentId, activeContentId, nodeContentMap])

  // --- LOCAL UI STATE ---
  // Start with content visible if status is PROCESSING or there's already content
  const [showContent, setShowContent] = useState<boolean>(
    status === DragTreeNodeContentStatus.PROCESSING ||
      (status === DragTreeNodeContentStatus.ACTIVE &&
        storeContent.trim().length > 0)
  )
  const [isPersistent, setIsPersistent] = useState<boolean>(false)
  const [isEditable, setIsEditable] = useState<boolean>(false)
  const [isSaving, setIsSaving] = useState<boolean>(false)
  const [justSaved, setJustSaved] = useState<boolean>(false)
  const [currentSearchQuery, setCurrentSearchQuery] = useState<string>('')
  const [isSearching, setIsSearching] = useState<boolean>(false)
  const [isStuckProcessing, setIsStuckProcessing] = useState<boolean>(false)

  // Local state for user-editable content. Synced from the store or stream.
  const [localContent, setLocalContent] = useState<string>(storeContent)

  const hasCopyableContent = useMemo<boolean>(() => {
    if (isStreaming && localContent.trim().length > 0) {
      return true
    }

    if (storedNodeContent) {
      if (storedNodeContent.contentText.trim().length > 0) {
        return true
      }

      const metadata = storedNodeContent.metadata
      if (metadata) {
        const metadataCandidates = [metadata.summary, metadata.content]
        if (
          metadataCandidates.some(
            value => typeof value === 'string' && value.trim().length > 0
          )
        ) {
          return true
        }
      }

      if (
        Array.isArray(storedNodeContent.messages) &&
        storedNodeContent.messages.length > 0
      ) {
        return true
      }
    }

    return localContent.trim().length > 0
  }, [isStreaming, localContent, storedNodeContent])

  const userId = (session?.user as { id?: string } | undefined)?.id

  // Copy helper ensures TipTap JSON (if present) becomes markdown before copying.
  const handleCopyQuickResearch = useCallback(async () => {
    let copyOrigin: CopyOrigin = 'local'
    let textToCopy = ''

    const streamingContentTrimmed = localContent.trim()
    if (isStreaming && streamingContentTrimmed.length > 0) {
      copyOrigin = 'stream'
      textToCopy = streamingContentTrimmed
    }

    if (!textToCopy && storedNodeContent) {
      const derivedFromStore = extractNodeContentText(storedNodeContent).trim()
      if (derivedFromStore.length > 0) {
        copyOrigin = 'stored'
        textToCopy = derivedFromStore
      }
    }

    if (!textToCopy) {
      const derivedFromLocal = extractNodeContentText({
        contentText: localContent,
      }).trim()
      if (derivedFromLocal.length > 0) {
        copyOrigin = 'local'
        textToCopy = derivedFromLocal
      }
    }

    if (textToCopy.length === 0) {
      toast.error('No quick research content available to copy yet.')
      logEventWithContext(
        'click_copy_quickResearch_empty',
        userId,
        dragTreeId || undefined,
        {
          node_id: nodeId,
          content_id: workingContentId ?? null,
        }
      )
      return
    }

    try {
      if (
        typeof navigator === 'undefined' ||
        !navigator.clipboard ||
        typeof navigator.clipboard.writeText !== 'function'
      ) {
        throw new Error('Clipboard API unavailable')
      }

      await navigator.clipboard.writeText(textToCopy)
      toast.success('Quick research copied to clipboard.')

      logEventWithContext('click_copy_quickResearch', userId, dragTreeId || undefined, {
        node_id: nodeId,
        content_id: workingContentId ?? null,
        copy_origin: copyOrigin,
        copied_length: textToCopy.length,
      })
    } catch (error) {
      console.error('Failed to copy quick research content:', error)
      toast.error('Unable to copy quick research. Try again.')

      logEventWithContext(
        'click_copy_quickResearch_failure',
        userId,
        dragTreeId || undefined,
        {
          node_id: nodeId,
          content_id: workingContentId ?? null,
          copy_origin: copyOrigin,
          error_message: error instanceof Error ? error.message : 'unknown',
        }
      )
    }
  }, [
    userId,
    dragTreeId,
    nodeId,
    workingContentId,
    isStreaming,
    localContent,
    storedNodeContent,
  ])

  // This effect ensures localContent is updated from the store when not streaming.
  useEffect(() => {
    // When streaming, the content is driven by the stream.
    // When not streaming, sync with the store's version.
    if (!isStreaming) {
      setLocalContent(storeContent)
    }
  }, [storeContent, isStreaming])

  // Auto-start research when content is in INITIALIZED status
  useEffect(() => {
    if (status === DragTreeNodeContentStatus.INITIALIZED && workingContentId) {
      // Small delay to ensure store is fully updated
      setTimeout(() => {
        startResearch(workingContentId) // Pass the existing content ID
      }, 100)
    }
  }, [status, workingContentId, startResearch])

  // Auto-convert to TipTap editor when OpenAI native streaming completes
  useEffect(() => {
    if (
      !isStreaming &&
      status === DragTreeNodeContentStatus.ACTIVE &&
      !showTipTapEditor
    ) {
      // Extract content from the final assistant message
      const finalAssistantMessage = messages
        .filter(msg => msg.role === 'assistant')
        .pop()

      if (finalAssistantMessage && finalAssistantMessage.content) {
        let content = ''
        if (typeof finalAssistantMessage.content === 'string') {
          content = finalAssistantMessage.content
        } else if (Array.isArray(finalAssistantMessage.content)) {
          content = finalAssistantMessage.content
            .filter((part: any) => part.type === 'text')
            .map((part: any) => part.text)
            .join(' ')
        }

        if (content.trim()) {
          handleConvertToEditor(content)
        }
      }
    }
  }, [isStreaming, status, messages, showTipTapEditor, handleConvertToEditor])

  // Keep localContent in sync with streamingContent during streaming
  useEffect(() => {
    if (isStreaming && streamingContent) {
      setLocalContent(streamingContent)
    }
  }, [isStreaming, streamingContent])

  // Lazy-load content when user tries to view but content missing
  useEffect(() => {
    if (
      showContent &&
      status === DragTreeNodeContentStatus.ACTIVE &&
      storeContent.trim().length === 0 &&
      workingContentId
    ) {
      fetchNodeContent(nodeId, workingContentId)
    }
  }, [
    showContent,
    status,
    storeContent,
    workingContentId,
    nodeId,
    fetchNodeContent,
  ])

  // Ensure messages (steps) are loaded for completed research on initial/tab load
  useEffect(() => {
    if (
      status === DragTreeNodeContentStatus.ACTIVE &&
      workingContentId &&
      (!Array.isArray(stepMessages) || stepMessages.length === 0)
    ) {
      // Fetch full content including messages; internal guard prevents duplicate fetches
      fetchNodeContent(nodeId, workingContentId)
    }
  }, [status, workingContentId, stepMessages, nodeId, fetchNodeContent])

  // Track whether the current session is/was streaming so we can detect the
  // precise moment a stream finishes.
  const wasStreamingRef = useRef<boolean>(false)

  // Mark that we are streaming when `isStreaming` flips to true.
  useEffect(() => {
    if (isStreaming) {
      wasStreamingRef.current = true
    }
  }, [isStreaming])

  // When streaming ends AND the status is COMPLETED, auto-pin the block so it
  // remains visible. This only runs immediately after a successful stream,
  // leaving pre-existing completed research untouched (user needs to hover or
  // click as before).
  useEffect(() => {
    if (
      !isStreaming &&
      wasStreamingRef.current &&
      status === DragTreeNodeContentStatus.ACTIVE
    ) {
      setIsPersistent(true)
      setShowContent(true)
      setHoverCollapseLock(true)

      // reset flag so future loads don't retrigger
      wasStreamingRef.current = false
    }
  }, [isStreaming, status, setHoverCollapseLock])

  // Refs for timeouts
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const justSavedTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Prioritize streaming content if active
  const actualContent =
    isStreaming && streamingContent ? streamingContent : storeContent

  const debouncedSave = useCallback(
    debounce((newContentId: string, newContentText: string) => {
      updateNodeContent(nodeId, newContentId, { contentText: newContentText })
      setIsSaving(false)
      setJustSaved(true)
      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
      justSavedTimeoutRef.current = setTimeout(() => {
        setJustSaved(false)
      }, 2000)
    }, 1000),
    [nodeId, updateNodeContent]
  )

  // --- HOOKS (SIDE EFFECTS) ---

  useEffect(() => {
    // Sync local state with external content (from store or streaming).
    if (actualContent !== localContent) {
      setLocalContent(actualContent)
    }
  }, [actualContent])

  useEffect(() => {
    if (isPersistent) {
      setShowContent(true)
    }
  }, [isPersistent])

  // Consolidated visibility logic - this is the master effect that controls showContent
  useEffect(() => {
    // Priority 1: Always show during streaming, processing, or initializing
    if (
      isStreaming ||
      status === DragTreeNodeContentStatus.PROCESSING ||
      status === DragTreeNodeContentStatus.INITIALIZED
    ) {
      setShowContent(true)
      return
    }

    // Priority 2: Show if persistent (user pinned it)
    if (isPersistent) {
      setShowContent(true)
      return
    }

    // Priority 3: Hide if not persistent and not streaming/processing
    setShowContent(false)
  }, [isStreaming, isPersistent, status])

  // Force show content when streaming starts
  useEffect(() => {
    if (isStreaming) {
      setShowContent(true)
      console.log(
        '🔬 [ResearchDisplay] Streaming started, showing content area'
      )
    }
  }, [isStreaming])

  // Timeout detection for stuck PROCESSING status
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | undefined

    if (status === DragTreeNodeContentStatus.PROCESSING && !isStreaming) {
      console.log(
        `⏰ [ResearchDisplay] Starting timeout detection for node: ${nodeId}`
      )

      // Reset stuck state when processing starts
      setIsStuckProcessing(false)

      // Set a 6-minute timeout (slightly longer than API timeout)
      timeoutId = setTimeout(
        () => {
          console.warn(
            `⏰ [ResearchDisplay] Research appears stuck for node: ${nodeId}`
          )
          setIsStuckProcessing(true)
        },
        6 * 60 * 1000
      ) // 6 minutes
    } else {
      // Reset stuck state when not processing
      setIsStuckProcessing(false)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [status, isStreaming, nodeId])

  // Removed global research preview effect to prevent shared state issues
  // Each component now manages its own state independently

  // Monitor streaming for search activity - improved detection
  useEffect(() => {
    if (isStreaming) {
      // Check if streaming content indicates search activity
      if (streamingContent) {
        // Look for search patterns in the streaming content
        const searchPatterns = [
          /searching.*?for.*?"([^"]+)"/i,
          /searching.*?"([^"]+)"/i,
          /search.*?"([^"]+)"/i,
          /web search.*?"([^"]+)"/i,
        ]

        let foundQuery = null
        for (const pattern of searchPatterns) {
          const match = streamingContent.match(pattern)
          if (match && match[1]) {
            foundQuery = match[1]
            break
          }
        }

        if (foundQuery) {
          setCurrentSearchQuery(foundQuery)
          setIsSearching(true)
        } else if (
          streamingContent.includes('found') ||
          streamingContent.includes('results')
        ) {
          setIsSearching(false)
        }
      }
    } else {
      // Clear search state when streaming ends
      setIsSearching(false)
      setCurrentSearchQuery('')
    }
  }, [isStreaming, streamingContent])

  // Effect to release lock on unmount
  useEffect(() => {
    return () => {
      if (isPersistent) {
        setHoverCollapseLock(false)
      }
      if (justSavedTimeoutRef.current) clearTimeout(justSavedTimeoutRef.current)
    }
  }, [isPersistent, setHoverCollapseLock])

  // --- EVENT HANDLERS & RENDER LOGIC ---

  // Legacy textarea editing removed for v2 flow
  // const handleContentChange = (
  //   event: React.ChangeEvent<HTMLTextAreaElement>
  // ) => {
  //   const newContent = event.target.value
  //   setLocalContent(newContent)
  //
  //   if (workingContentId) {
  //     setIsSaving(true)
  //     debouncedSave(workingContentId, newContent)
  //   }
  // }

  const handleMouseEnter = () => {
    if (hideTimeoutRef.current) clearTimeout(hideTimeoutRef.current)
    // Show content only if enableHoverPreview is true (removed global preview dependency)
    if (enableHoverPreview && !isPersistent && !isStreaming) {
      setShowContent(true)
      setHoverCollapseLock(true)
    }
  }

  const handleMouseLeave = () => {
    if (hoverTimeoutRef.current) clearTimeout(hoverTimeoutRef.current)
    // Hide content only if enableHoverPreview is true (removed global preview dependency)
    if (
      enableHoverPreview &&
      !isPersistent &&
      status !== DragTreeNodeContentStatus.PROCESSING
    ) {
      hideTimeoutRef.current = setTimeout(() => {
        setShowContent(false)
        setHoverCollapseLock(false)
      }, 300)
    }
  }

  // Unified toggle visibility handler (replaces button hover/click handlers)
  const handleToggleVisibility = async () => {
    // Handle stuck processing state - allow retry
    if (
      isStuckProcessing ||
      (status === DragTreeNodeContentStatus.PROCESSING && isStuckProcessing)
    ) {
      console.log(
        `🔄 [ResearchDisplay] Retrying stuck research for node: ${nodeId}`
      )
      setIsStuckProcessing(false)
      await startResearch()
      return
    }

    if (nodeContentMap && nodeContentMap.size > 0) {
      const nextIsPersistent = !isPersistent
      setIsPersistent(nextIsPersistent)
      setShowContent(nextIsPersistent)
      setHoverCollapseLock(nextIsPersistent)
    } else {
      // Starting research: Just kick off the process. The effects will
      // handle showing the content and pinning it upon completion.
      await startResearch()
    }
  }

  const getPlaceholderText = () => {
    switch (status) {
      case DragTreeNodeContentStatus.PROCESSING:
        return 'Generating research content...'
      case DragTreeNodeContentStatus.INITIALIZED:
        return 'Research starting...'
      case null:
        return 'Click the research button to start.'
      default:
        return 'Research content'
    }
  }

  const handleTextareaClick = (event: React.MouseEvent) => {
    // Prevent click from bubbling to parent nodes
    event.stopPropagation()

    if (!isEditable) {
      setIsEditable(true)
    }
    if (!isPersistent) {
      setIsPersistent(true)
      setShowContent(true)
      setHoverCollapseLock(true)
    }
  }

  const handleOpenInTab = async () => {
    if (localContent.trim() && workingContentId) {
      // Mark content as read optimistically for immediate UI feedback
      const isUnread = !isContentRead(nodeId, workingContentId)
      if (isUnread) {
        markContentAsReadOptimistic(nodeId, workingContentId)

        // Call server action to persist the read status
        try {
          const { markContentAsRead } = await import(
            '@/app/server-actions/drag-tree/research-update'
          )
          const result = await markContentAsRead(workingContentId)
          if (!result.success) {
            console.error('Failed to mark content as read:', result.error)
            // Note: We don't revert the optimistic update since it's not critical
          }
        } catch (error) {
          console.error('Error marking content as read:', error)
        }
      }

      // Extract just the question part from the full path
      const pathParts = fullQuestionPath.split(' > ')
      const questionOnly = pathParts[pathParts.length - 1] || fullQuestionPath

      const tabTitle =
        questionOnly.length > 30
          ? `${questionOnly.substring(0, 30)}...`
          : questionOnly

      addTab({
        title: tabTitle,
        fullTitle: fullQuestionPath,
        type: 'research',
        nodeId,
        contentId: workingContentId,
        isClosable: true,
      })
    }
  }

  // --- CONDITIONAL RENDER (The final step) ---
  if (!nodeContentMap) {
    return null
  }

  return (
    <div
      className={cn('w-full max-w-full overflow-x-hidden', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Subtle Research Button */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <QuickResearchHeaderSection
            status={status}
            isPersistent={isPersistent}
            isStreaming={isStreaming}
            isStuckProcessing={isStuckProcessing}
            stepMessages={stepMessages}
            localContent={localContent}
            onToggleVisibility={handleToggleVisibility}
            onOpenInTab={handleOpenInTab}
            onCopy={handleCopyQuickResearch}
            canCopy={hasCopyableContent}
            onOpenSteps={async () => {
              if (
                status === DragTreeNodeContentStatus.ACTIVE &&
                workingContentId
              ) {
                await fetchNodeContent(nodeId, workingContentId)
              }
            }}
          />
        </div>
      </div>

      {/* Search Progress Indicator */}
      {isSearching && (
        <div className="mt-2">
          <SearchProgressIndicator
            isSearching={isSearching}
            currentQuery={currentSearchQuery}
          />
        </div>
      )}

      {/* Animated Content */}
      <div
        className={cn(
          'transition-all duration-500 ease-in-out overflow-hidden',
          'w-full max-w-full',
          {
            'max-h-0 opacity-0 mt-0': !showContent,
            // Use different max heights based on constrainHeight prop
            [constrainHeight
              ? 'max-h-[400px] opacity-100 mt-3'
              : 'max-h-[1000px] opacity-100 mt-3']: showContent,
          }
        )}
        style={{
          width: '100%',
          maxWidth: '100%',
          minWidth: 0,
          boxSizing: 'border-box',
        }}
      >
        <div
          className={cn(
            'relative w-full max-w-full',
            constrainHeight ? 'h-full' : ''
          )}
          style={{
            width: '100%',
            maxWidth: '100%',
            minWidth: 0,
            boxSizing: 'border-box',
            overflow: 'hidden',
            ...(constrainHeight ? { height: '350px' } : {}),
          }}
        >
          {/* Show loading indicator during INITIALIZED, new streaming UI for OpenAI native, TiptapQuickResearchEditor when complete */}
          {status === DragTreeNodeContentStatus.INITIALIZED ? (
            <div className="flex items-center justify-center h-32 text-sm text-gray-400">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Preparing research...
            </div>
          ) : isStreaming || status === DragTreeNodeContentStatus.PROCESSING ? (
            // In v2 (OpenAI native search), always use streaming UI. Legacy (v1) no longer shows textarea here.
            <QuickResearchStreamingDisplay
              messages={messages}
              // Treat PROCESSING/INITIALIZED as streaming for step-one spinner UX
              isStreaming={
                isStreaming ||
                status === DragTreeNodeContentStatus.PROCESSING ||
                status === (DragTreeNodeContentStatus as any).INITIALIZED
              }
              onConvertToEditor={handleConvertToEditor}
              className={cn(
                'w-full',
                constrainHeight ? 'h-full' : 'min-h-[200px]'
              )}
            />
          ) : showTipTapEditor ? (
            <TiptapQuickResearchEditor
              content={localContent || ''}
              onContentChange={(newContent: string) => {
                setLocalContent(newContent)
                if (workingContentId) {
                  setIsSaving(true)
                  debouncedSave(workingContentId, newContent)
                }
              }}
              onJSONChange={(jsonContent: JSONContent) => {
                const jsonString = JSON.stringify(jsonContent)
                setLocalContent(jsonString)
                if (workingContentId) {
                  setIsSaving(true)
                  debouncedSave(workingContentId, jsonString)
                }
              }}
              isReadOnly={isReadOnlyDueToPermissions}
              isStreaming={false}
              placeholder={getPlaceholderText()}
              onClick={handleTextareaClick}
              className="max-w-full"
              showBubbleMenu={!constrainHeight}
              debounceMs={1000}
              autoFocus={false}
              compact={!constrainHeight}
              questionText={questionText}
              questionNodeId={nodeId}
              showResearchButton={false}
            />
          ) : null}
        </div>

        {/* Status Footer */}
        <QuickResearchStatusFooter
          isStreaming={isStreaming}
          isProcessing={status === DragTreeNodeContentStatus.PROCESSING}
          isEditable={isEditable}
          isSaving={isSaving}
          justSaved={justSaved}
          hasContent={Boolean(localContent)}
        />
      </div>
    </div>
  )
}

// Wrap with error boundary for better error handling
const QuickResearchDisplayWithErrorBoundary: React.FC<
  ResearchDisplayProps
> = props => (
  <ResearchErrorBoundary>
    <ResearchDisplay {...props} />
  </ResearchErrorBoundary>
)

// Memoize to prevent unnecessary re-renders when props haven't changed
export default React.memo(QuickResearchDisplayWithErrorBoundary)
